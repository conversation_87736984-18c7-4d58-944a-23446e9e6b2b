#!/usr/bin/env python3
"""
Test script to verify that the currency validation spam issue is fixed
"""

import asyncio
import logging
import sys
import os
from decimal import Decimal
from typing import Dict, Any

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_currency_validation():
    """Test the enhanced cross-currency arbitrage currency validation"""
    try:
        logger.info("🧪 [TEST] Testing currency validation fix...")
        
        # Import the enhanced arbitrage engine
        from src.trading.enhanced_cross_currency_arbitrage import EnhancedCrossCurrencyArbitrage
        
        # Create a mock exchange client
        class MockExchangeClient:
            async def get_balance(self, currency=None):
                if currency:
                    return 100.0  # Return some balance
                else:
                    # Return balances with problematic prefixes to test the fix
                    return {
                        'bybit_USDT': 100.0,
                        'USDT': 50.0,
                        'BTC': 0.001,
                        'ETH': 0.1,
                        'SOL': 10.0
                    }
        
        # Create mock exchange clients
        exchange_clients = {
            'bybit': MockExchangeClient(),
            'coinbase': MockExchangeClient()
        }
        
        # Initialize the arbitrage engine
        arbitrage_engine = EnhancedCrossCurrencyArbitrage(
            exchange_clients=exchange_clients,
            config={
                'min_profit_threshold': 0.005,
                'max_execution_time': 30.0,
                'max_risk_score': 0.7
            }
        )
        
        logger.info("✅ [TEST] Arbitrage engine initialized successfully")
        
        # Test currency validation directly
        logger.info("🔍 [TEST] Testing currency validation with problematic currencies...")
        
        test_currencies = [
            'bybit_USDT',  # Should be cleaned to 'USDT'
            'USDT',        # Should be valid
            'BTC',         # Should be valid
            'ETH',         # Should be valid
            'INVALID',     # Should be invalid
            'coinbase_BTC' # Should be cleaned to 'BTC'
        ]
        
        for currency in test_currencies:
            is_valid = arbitrage_engine._is_valid_currency(currency)
            logger.info(f"   Currency '{currency}': {'✅ Valid' if is_valid else '❌ Invalid'}")
        
        # Test balance fetching (this was causing the spam)
        logger.info("🔍 [TEST] Testing balance fetching...")
        balances = await arbitrage_engine._get_available_balances()
        logger.info(f"✅ [TEST] Balances fetched: {balances}")
        
        # Test arbitrage opportunity scanning (this was triggering the validation loop)
        logger.info("🔍 [TEST] Testing arbitrage opportunity scanning...")
        opportunities = await arbitrage_engine.scan_arbitrage_opportunities(balances)
        logger.info(f"✅ [TEST] Arbitrage scan completed. Found {len(opportunities)} opportunities")
        
        logger.info("🎉 [TEST] All tests completed successfully!")
        logger.info("✅ [TEST] Currency validation spam issue appears to be fixed!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ [TEST] Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    logger.info("🚀 [TEST] Starting currency validation fix test...")
    
    success = await test_currency_validation()
    
    if success:
        logger.info("✅ [TEST] Currency validation fix test PASSED")
        return 0
    else:
        logger.error("❌ [TEST] Currency validation fix test FAILED")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("🛑 [TEST] Test interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"💥 [TEST] Test crashed: {e}")
        sys.exit(1)
